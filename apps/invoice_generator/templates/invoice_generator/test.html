<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Invoice</title>
  <style>
    :root {
      --brand-color: #4A90E2;
      --text-color: #333333;
      --bg-color: #FFFFFF;
      --accent-bg: #F0F4FA;
      --font-sans: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    *, *::before, *::after {
      box-sizing: border-box;
    }
    body {
      margin: 0;
      padding: 20px;
      font-family: var(--font-sans);
      color: var(--text-color);
      background-color: #F7F8FA;
    }
    .invoice {
      max-width: 800px;
      margin: auto;
      background: var(--bg-color);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: var(--brand-color);
      color: #fff;
      padding: 20px;
    }
    .header .logo img {
      max-height: 40px;
    }
    .header .company-info {
      text-align: right;
      line-height: 1.4;
    }
    .company-info .name {
      font-size: 1.2em;
      font-weight: bold;
    }
    .details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      padding: 20px;
    }
    .details div {
      background: var(--accent-bg);
      padding: 15px;
      border-radius: 4px;
    }
    .items {
      width: 100%;
      border-collapse: collapse;
      margin: 0;
    }
    .items th, .items td {
      padding: 12px;
      border-bottom: 1px solid #E1E4E8;
      text-align: left;
    }
    .items th {
      background: var(--accent-bg);
    }
    .items .total-row td {
      border-top: 2px solid var(--brand-color);
      font-weight: bold;
    }
    .notes {
      padding: 20px;
      font-size: 0.9em;
      background: #FAFAFB;
      border-top: 1px solid #E1E4E8;
    }
    @media (max-width: 600px) {
      .header, .details {
        flex-direction: column;
        text-align: center;
      }
      .details {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="invoice">
    <div class="header">
      <div class="logo">
        <img src="https://via.placeholder.com/150x40?text=LOGO" alt="Logo">
      </div>
      <div class="company-info">
        <div class="name" contenteditable="true">Your Company Name</div>
        <div contenteditable="true">123 Business Rd.</div>
        <div contenteditable="true">Business City, BC 12345</div>
        <div contenteditable="true"><EMAIL></div>
      </div>
    </div>
    <div class="details">
      <div>
        <strong>Invoice #:</strong> <span contenteditable="true">INV-0001</span>
      </div>
      <div>
        <strong>Date:</strong> <span contenteditable="true">2025-05-29</span>
      </div>
      <div>
        <strong>Due Date:</strong> <span contenteditable="true">2025-06-13</span>
      </div>
      <div>
        <strong>Bill To:</strong> <div contenteditable="true">Client Name<br>Client Address</div>
      </div>
    </div>
    <table class="items">
      <thead>
        <tr>
          <th>Description</th>
          <th>Qty</th>
          <th>Unit Price</th>
          <th>Amount</th>
        </tr>
      </thead>
      <tbody contenteditable="true">
        <tr>
          <td>Service or Product</td>
          <td>1</td>
          <td>$100.00</td>
          <td>$100.00</td>
        </tr>
      </tbody>
      <tfoot>
        <tr class="total-row">
          <td colspan="3">Subtotal</td>
          <td>$100.00</td>
        </tr>
        <tr>
          <td colspan="3">Tax (10%)</td>
          <td>$10.00</td>
        </tr>
        <tr class="total-row">
          <td colspan="3">Total</td>
          <td>$110.00</td>
        </tr>
      </tfoot>
    </table>
    <div class="notes" contenteditable="true">
      Thank you for your business. Please remit payment within 15 days.
    </div>
  </div>
</body>
</html>
